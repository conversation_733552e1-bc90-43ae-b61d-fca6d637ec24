using GolfFantaModule.Common;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.DTO.Player;
using GolfFantaCore.DTOs;
using GolfFantaCore.Entities.Database;
using GolfFantaCore.Models;
using GolfFantaCore.Models.Bag;
using GolfFantaCore.Models.Common;
using GolfFantaCore.Models.Economy;
using GolfFantaCore.Models.Economy.GearCustom;
using GolfFantaCore.Models.Economy.PlayerInventory;
using GolfFantaCore.Models.HeadToHead;
using GolfFantaCore.Models.Ranking;
using GolfFantaCore.Models.Tour;
using GolfFantaModule.CustomService.Interfaces;
using GolfFantaModule.Extensions;
using GolfFantaModule.SDKs;
using GolfFantaModule.Supabase.Services.Interfaces;
using GolfFantaModule.UnityGameServices.Services.Interfaces;
using Supabase.Core;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using Unity.Services.CloudCode.Shared;
using Unity.Services.Leaderboards.Model;
using Unity.Services.Economy.Model;
using Constants = Supabase.Postgrest.Constants;
using CloudCodeConstant = GolfFantaCore.Common.Constants;

namespace GolfFantaModule.Modules
{
    public class MatchModule : APIModule
    {
        private readonly IChallengeService _challengeService;

        public MatchModule(
            ILogger<GolfFantaModule> _logger,
            IExecutionContext _context,
            IGameApiClient _gameApiClient,
            IAdminApiClient _adminApiClient,
            ICloudSaveServices cloudSaveServices,
            IRemoteConfigServices remoteConfigServices,
            IEconomyServices economyServices,
            IInternalDatabaseService internalDatabaseService,
            ICustomServices customServices,
            IChallengeService challengeService,
            APIClientRequest apiClientRequest
        ) : base(_logger, _context, _gameApiClient, _adminApiClient, cloudSaveServices, remoteConfigServices,
            economyServices, internalDatabaseService, customServices, apiClientRequest)
        {
            _challengeService = challengeService;
        }

        public override async Task<APIServerResponse> Process()
        {
            if (IsSessionValid(ApiClientRequest.sessionId).Result == false)
            {
                APIServerResponse response = new APIServerResponse(code: ApiClientRequest.code,
                    responseCode: APIResponseCode.Fail_SessionInvalid, time: DateTime.Now, data: "Session invalid.");
                return response;
            }

            switch (ApiClientRequest.code)
            {
                case APIRequestMethod.GetHeadToHeadMatchResult:
                {
                    return await GetHeadToHeadMatchResult(ApiClientRequest);
                }
                case APIRequestMethod.FailBallActionResult:
                {
                    return await FailBallActionResult(ApiClientRequest);
                }
                case APIRequestMethod.FinishMatchClubResult:
                {
                    return await FinishMatchClubResult(ApiClientRequest);
                }
                case APIRequestMethod.ConsumeGearResult:
                {
                    return await ConsumeGearResult(ApiClientRequest);
                }
                case APIRequestMethod.GetH2hLeaderboard:
                {
                    return await GetLeaderboard(ApiClientRequest);
                }
                case APIRequestMethod.GetH2hLeaderboardByPlayerIds:
                {
                    return await GetLeaderboardByPlayerIds(ApiClientRequest);
                }
                case APIRequestMethod.ConsumeCoinFee:
                {
                    return await ConsumeCoinFee(ApiClientRequest);
                }
                case APIRequestMethod.ConsumeInventoryResult:
                {
                    return await ConsumeInventoryItemResult(ApiClientRequest);
                }
                default:
                {
                    APIServerResponse response = new APIServerResponse(code: ApiClientRequest.code,
                        responseCode: APIResponseCode.Fail_CodeIneligible, time: DateTime.Now,
                        data: "Code has no function.");
                    return response;
                }
            }
        }

        /// <summary>
        /// Consume Inventory Result
        /// </summary>
        /// <param name="apiClientRequest">data model is ConsumeInventoryItemModel</param>
        /// <returns></returns>
        private async Task<APIServerResponse> ConsumeInventoryItemResult(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);

            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now,
                    data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            string configAssignmentHash = parameters[0];

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            var inventoryItems = JsonConvert.DeserializeObject<ConsumeInventoryItemModel>(parameters[1])!;

            List<string> itemIds = new();
            List<int> quantities = new();
            Task? trackingConsumeBall = null;

            if (inventoryItems.ConsumeBalls != null)
            {
                for (var index = 0; index < inventoryItems.ConsumeBalls.ItemIds.Count; index++)
                {
                    var ball = inventoryItems.ConsumeBalls.ItemIds[index];
                    var ballItem = playerInventory.PlayerBallInventoryItems.Find(item => item.id == ball);

                    if (ballItem == null || ballItem.instanceData.quantity == 0)
                    {
                        SimpleRespone respone = new SimpleRespone();
                        respone.data = "Not valid ball action.";
                        APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                            responseCode: APIResponseCode.Fail, time: DateTime.Now,
                            data: JsonConvert.SerializeObject(respone));
                        return newReponse;
                    }

                    var ballQuantity = 0;

                    var updateQuantity = inventoryItems.ConsumeBalls.Quantities[index];

                    if (ballItem.instanceData.quantity > 0 && updateQuantity >= 0) ballQuantity = -updateQuantity;

                    if (ballQuantity != 0)
                    {
                        itemIds.Add(inventoryItems.ConsumeBalls.ItemIds[index]);
                        quantities.Add(ballQuantity);
                    }
                }

                trackingConsumeBall = _challengeService.TrackConsumeBall(quantities.Sum());
            }

            if (inventoryItems.ConsumeGears != null)
            {
                for (var index = 0; index < inventoryItems.ConsumeGears.ItemIds.Count; index++)
                {
                    var gear = inventoryItems.ConsumeGears.ItemIds[index];
                    var gearItem = playerInventory.PlayerGearInventoryItems.Find(item => item.id == gear);

                    if (gearItem == null || gearItem.instanceData.quantity == 0)
                    {
                        SimpleRespone respone = new SimpleRespone();
                        respone.data = "Not valid ball action.";
                        APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                            responseCode: APIResponseCode.Fail, time: DateTime.Now,
                            data: JsonConvert.SerializeObject(respone));
                        return newReponse;
                    }

                    var gearQuantity = 0;

                    var updateQuantity = inventoryItems.ConsumeGears.Quantities[index];

                    if (gearItem.instanceData.quantity > 0 && updateQuantity >= 0) gearQuantity = -updateQuantity;

                    if (gearQuantity != 0)
                    {
                        itemIds.Add(inventoryItems.ConsumeGears.ItemIds[index]);
                        quantities.Add(gearQuantity);
                    }
                }
            }

            if (itemIds.Count > 0)
            {
                await EconomyServices.AddItemsToInventory(
                    context,
                    gameApiClient,
                    itemIds,
                    quantities,
                    false,
                    context.PlayerId!, null);
            }

            if (inventoryItems.ConsumeClubs != null)
            {
                var clubItems = new List<PlayerClubInventoryItem>();
                foreach (var clubId in inventoryItems.ConsumeClubs.ItemIds)
                {
                    var id = clubId;

                    var clubItem = playerInventory.PlayerClubInventoryItems.Find(item => item.id == id);

                    if (clubItem is not { instanceData.capacity: > 0 }) continue;

                    clubItem.instanceData.capacity--;
                    clubItems.Add(clubItem);
                }

                foreach (var clubItem in clubItems)
                {
                    _ = EconomyServices.UpdatePlayerClubInventoryItem(
                        context,
                        gameApiClient,
                        clubItem.uid,
                        clubItem.instanceData.fragment,
                        clubItem.instanceData.capacity,
                        clubItem.instanceData.level,
                        0,
                        context.PlayerId!,
                        false,
                        configAssignmentHash
                    );
                }
            }

            var simpleResponse = new SimpleRespone
            {
                data = "Update Inventory Done!"
            };

            var serverResponse = new APIServerResponse
            {
                code = apiClientRequest.code,
                time = DateTime.Now,
                responseCode = APIResponseCode.Success,
                data = JsonConvert.SerializeObject(simpleResponse)
            };

            if (trackingConsumeBall != null) await trackingConsumeBall;

            return serverResponse;
        }

        /// <summary>
        /// Get Head To Head Match Result
        /// </summary>
        /// <param name="apiClientRequest">data model is HeadToHeadResultRequestModel</param>
        /// <returns></returns>
        public async Task<APIServerResponse> GetHeadToHeadMatchResult(APIClientRequest apiClientRequest)
        {
            var responseToClient = new APIServerResponse()
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };

            var result = new HeadToHeadMatchResultResponse();
            result.rewardItems = new List<EconomyItem>();
            var requestModel = JsonConvert.DeserializeObject<HeadToHeadResultRequestModel>(apiClientRequest.data);
            if (requestModel?.headToHeadMatchProperties?.playerIds == null ||
                requestModel.headToHeadMatchProperties.playerIds.Count == 0)
            {
                responseToClient.responseCode = APIResponseCode.Fail;
                responseToClient.time = DateTime.Now;
                responseToClient.message = "Request data is null";
                return responseToClient;
            }

            Task? trackWinModeGame = null;

            try
            {
                HeadToHeadMatchProperties matchProperties = requestModel.headToHeadMatchProperties!;
                var gameStats = matchProperties.gameStats;
                string configAssignmentHash = requestModel.configAssignmentHash!;

                // read ranking data from remote config
                List<RankingConfig> rankingConfigs =
                    await RemoteConfigServices.GetPlayerRankingConfig(context, gameApiClient);

                // for each player in match
                for (int i = 0; i < matchProperties.playerIds!.Count; i++)
                {
                    // only process request from sender
                    if (!matchProperties.playerIds[i].Equals(context.PlayerId))
                    {
                        continue;
                    }

                    var player = await DatabaseService.From<PlayerEntity>()
                        .Filter("user_id", Constants.Operator.Equals, DatabaseService.GetUserId())
                        .Single();
                    if (player == null)
                        throw new Exception(
                            $"Player not found. UserId: {DatabaseService.GetUserId()}. PlayerId: {context.PlayerId}");

                    var currentRank = player.RankId;

                    // update players rank data
                    var matchingRankConfig =
                        rankingConfigs.FirstOrDefault(rankConfig => rankConfig.id == player.RankId);
                    if (matchingRankConfig == null)
                        throw new Exception($"Could not find corresponding rank config {player.RankId}");

                    var isWonPlayer = i == matchProperties.winnerIndex;
                    var rankChanged = false;
                    if (isWonPlayer)
                    {
                        player.Elo += matchingRankConfig.headToHeadWin;
                        rankChanged = player.Elo > matchingRankConfig.maxElo;
                        player.TotalH2hGamesWon += 1;
                        player.H2hWinStreak += 1;
                        player.LongestH2hWinStreak = Math.Max(player.H2hWinStreak, player.LongestH2hWinStreak);
                        player.TotalH2hElo += matchingRankConfig.headToHeadWin;
                        player.TotalH2hCoinsWon += matchingRankConfig.entryFee * 2;
                    }
                    else
                    {
                        player.Elo = Math.Max(0, player.Elo - matchingRankConfig.headToHeadLose);
                        rankChanged = player.Elo < matchingRankConfig.minElo;
                        player.H2hWinStreak = 0;
                    }

                    if (isWonPlayer) trackWinModeGame = _challengeService.TrackWinModeGame(GameMode.HeadToHead);

                    // update rank
                    if (rankChanged)
                    {
                        var newRankConfig = rankingConfigs.FirstOrDefault(rankConfig =>
                            player.Elo >= rankConfig.minElo && player.Elo <= rankConfig.maxElo);
                        if (newRankConfig == null)
                            throw new Exception($"Could not find matching Rank config with Elo: {player.Elo}");
                        player.RankId = newRankConfig.id;

                        if (new Rank(newRankConfig.id!) > new Rank(player.HighestH2hRankId!))
                        {
                            player.HighestH2hRankId = player.RankId;
                            TrackingActivity(new PlayerActivityRequest(){ Type = PlayerActivityType.FirstReachRank, Data = player.RankId });
                        }
                    }

                    player.TotalH2hGames += 1;
                    player.LongestDrive = Math.Max(gameStats.longestShotDistance, player.LongestDrive);
                    if (gameStats.isHoleFinished)
                    {
                        player.HolesInOne += gameStats.strokeCount == 1 ? 1 : 0;
                        switch (gameStats.score)
                        {
                            case -1: player.Birdies++; break;
                            case -2: player.Eagles++; break;
                            case -3: player.Albatrosses++; break;
                        }

                        await _challengeService.TrackHoleResult(gameStats.score);
                    }

                    var fetchData = await DatabaseService.GetTourConfigs(GameMode.HeadToHead);

                    var h2hTour = fetchData.Select(c => c.ConvertTo<H2HTourModel>()).ToList();

                    var matchingTour = h2hTour.FirstOrDefault(tour => tour.Uuid.ToString() == matchProperties.tourUuid);

                    if (matchingTour == null)
                        throw new Exception("Could not find matching Tour" + matchProperties.tourUuid);

                    var currentEntryFeeConfig = matchingTour.EntryFees.Find(ef => ef.RankId == currentRank);

                    if (currentEntryFeeConfig == null)
                    {
                        var defaultConfig = matchingTour.EntryFees.Find((ef) =>
                            ef.RankId == CloudCodeConstant.DefaultTourConfigRankId);
                        currentEntryFeeConfig =
                            defaultConfig ?? throw new Exception($"Could not found matching fees config");
                    }

                    foreach (var item in currentEntryFeeConfig.Costs)
                    {
                        if (item.Quantity != 0 && Currencies.IsCurrency(item.ItemId))
                        {
                            if (isWonPlayer)
                            {
                                var resultCurrency = item.Quantity * 2;
                                if (item.ItemId == Currencies.Coin)
                                    resultCurrency = await CalculateResultCoin(resultCurrency, requestModel.headToHeadMatchProperties.gearBonus);
                                await gameApiClient.EconomyCurrencies.IncrementPlayerCurrencyBalanceAsync(context,
                                    context.AccessToken, context.ProjectId, context.PlayerId!,
                                    item.ItemId,
                                    new CurrencyModifyBalanceRequest(currencyId: item.ItemId, amount: resultCurrency));
                                result.rewardItems.Add(new EconomyItem()
                                    { ItemId = item.ItemId, Quantity = resultCurrency });
                            }
                        }
                    }

                    if (isWonPlayer)
                    {
                        BagModule bagModule = new BagModule(logger, context, gameApiClient, adminApiClient,
                            CloudSaveServices, RemoteConfigServices, EconomyServices, DatabaseService, CustomServices,
                            _challengeService, ApiClientRequest);

                        PlayerDataBagListModel bagListModelData = await bagModule.AddBag(player.RankId!, false,
                            matchProperties.playerIds[i], configAssignmentHash);

                        result.playerDataBagList = bagListModelData;
                    }

                    await DatabaseService.From<PlayerEntity>().Update(player);

                    result.playerInfoModel = DataConversionExtensions.ToPlayerInfoModel(player);

                    var saveLeaderboardResponse = await gameApiClient.Leaderboards.AddLeaderboardPlayerScoreAsync(
                        context, context.AccessToken, new Guid(context.ProjectId),
                        HeadToHeadConstant.HEAD_TO_HEAD_LEADERBOARD_ID, context.PlayerId!,
                        new AddLeaderboardScore(score: player.Elo));
                    if (saveLeaderboardResponse.StatusCode != HttpStatusCode.OK)
                        throw new Exception("Could not Save Leaderboard");
                }

                responseToClient.responseCode = APIResponseCode.Success;
            }
            catch (ApiException e)
            {
                responseToClient.responseCode = APIResponseCode.Fail;
                responseToClient.message = $"Failed to GetHeadToHeadMatchResult. Error: {e.Message}";
                logger.LogError("Failed to GetHeadToHeadMatchResult. Error: {Error}", e.Message);
            }

            if (trackWinModeGame != null)
                await trackWinModeGame;

            responseToClient.data = JsonConvert.SerializeObject(result);
            responseToClient.time = DateTime.Now;
            return responseToClient;
        }

        private async Task<APIServerResponse> FailBallActionResult(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);

            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now,
                    data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            string configAssignmentHash = parameters[0];

            List<string> ballIds = JsonConvert.DeserializeObject<List<string>>(parameters[1])!;

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            List<PlayerBallInventoryItem> ballItems = new List<PlayerBallInventoryItem>();

            foreach (var ball in ballIds)
            {
                var ballItem = playerInventory.PlayerBallInventoryItems.Find(item => item.id == ball);

                if (ballItem == null || ballItem.instanceData.quantity == 0)
                {
                    SimpleRespone respone = new SimpleRespone();
                    respone.data = "Not valid ball action.";
                    APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                        responseCode: APIResponseCode.Fail, time: DateTime.Now,
                        data: JsonConvert.SerializeObject(respone));
                    return newReponse;
                }

                ballItems.Add(ballItem);

                int ballQuantity = 0;
                if (ballItem.instanceData.quantity > 0) ballQuantity = -1;

                if (ballQuantity != 0)
                {
                    List<string> itemIds = new()
                    {
                        ball
                    };

                    List<int> quantities = new()
                    {
                        ballQuantity
                    };

                    await EconomyServices.AddItemsToInventory(
                        context,
                        gameApiClient,
                        itemIds,
                        quantities,
                        false,
                        context.PlayerId!,
                        null);

                    ballItem.instanceData.quantity -= ballQuantity;
                }
            }

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(ballItems);

            return serverResponse;
        }

        private async Task<APIServerResponse> ConsumeGearResult(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);

            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now,
                    data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            string configAssignmentHash = parameters[0];

            List<string> gearIds = JsonConvert.DeserializeObject<List<string>>(parameters[1])!;
            List<int> gearNumbers = JsonConvert.DeserializeObject<List<int>>(parameters[2])!;

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            List<string> itemIds = new();
            List<int> quantities = new();

            for (int i = 0; i < gearIds.Count; i++)
            {
                var gearItem = playerInventory.PlayerGearInventoryItems.Find(item => item.id == gearIds[i]);

                if (gearItem == null || gearItem.instanceData.quantity == 0)
                {
                    continue;
                }

                int gearQuantity = gearItem.instanceData.quantity;

                int gearQuantityUsed = gearNumbers[i];

                //int newGearQuantity = gearQuantity - gearQuantityUsed;
                //if (newGearQuantity < 0) newGearQuantity = 0;

                itemIds.Add(gearIds[i]);
                quantities.Add(gearQuantityUsed * -1);
            }

            if (itemIds.Count > 0)
            {
                await EconomyServices.AddItemsToInventory(
                    context,
                    gameApiClient,
                    itemIds,
                    quantities,
                    false,
                    context.PlayerId!,
                    null);

                APIServerResponse serverResponse = new APIServerResponse();

                serverResponse.code = apiClientRequest.code;
                serverResponse.time = DateTime.Now;
                serverResponse.responseCode = APIResponseCode.Success;
                serverResponse.data = "Success";

                return serverResponse;
            }
            else
            {
                SimpleRespone respone = new SimpleRespone();
                respone.data = "Not valid gear action.";
                APIServerResponse newReponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now, data: JsonConvert.SerializeObject(respone));
                return newReponse;
            }
        }

        private async Task<APIServerResponse> FinishMatchClubResult(APIClientRequest apiClientRequest)
        {
            List<string> parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);

            if (parameters == null)
            {
                SimpleRespone errorRespone = new SimpleRespone();
                errorRespone.data = "Data is null";
                APIServerResponse errorResponse = new APIServerResponse(code: apiClientRequest.code,
                    responseCode: APIResponseCode.Fail, time: DateTime.Now,
                    data: JsonConvert.SerializeObject(errorRespone));
                return errorResponse;
            }

            string configAssignmentHash = parameters[0];
            List<string> clubIds = JsonConvert.DeserializeObject<List<string>>(parameters[1])!;

            PlayerInventory playerInventory = await EconomyServices.GetPlayerInventory(
                context,
                gameApiClient,
                false,
                null,
                configAssignmentHash
            );

            var clubItems = new List<PlayerClubInventoryItem>();
            for (int i = 0; i < clubIds.Count; i++)
            {
                string clubId = clubIds[i];
                var clubItem = playerInventory.PlayerClubInventoryItems.Find(item => item.id == clubId);
                if (clubItem != null && clubItem.instanceData.capacity > 0)
                {
                    clubItem.instanceData.capacity--;
                    clubItems.Add(clubItem);
                }
            }

            foreach (var clubItem in clubItems)
            {
                //not await because doesn't need response
                _ = EconomyServices.UpdatePlayerClubInventoryItem(
                    context,
                    gameApiClient,
                    clubItem.uid,
                    clubItem.instanceData.fragment,
                    clubItem.instanceData.capacity,
                    clubItem.instanceData.level,
                    0,
                    context.PlayerId!,
                    false,
                    configAssignmentHash
                );
            }

            APIServerResponse serverResponse = new APIServerResponse();

            serverResponse.code = apiClientRequest.code;
            serverResponse.time = DateTime.Now;
            serverResponse.responseCode = APIResponseCode.Success;
            serverResponse.data = JsonConvert.SerializeObject(clubItems);

            return serverResponse;
        }

        /// <summary>
        /// Get Leaderboard
        /// </summary>
        /// <param name="apiClientRequest">apiClientRequest.data is json of data array</param>
        /// <param name="data[0]">leaderboardId</param>
        /// <param name="data[1]">offset, how many entries to skip</param>
        /// <param name="data[2]">limit, number of entries return</param>
        /// <returns>Response to client, data contain Leaderboard as Json</returns>
        private async Task<APIServerResponse> GetLeaderboard(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };

            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var leaderboardId = parameters[0];
                int? offset = int.TryParse(parameters[1], out var offsetValue) ? offsetValue : null;
                int? limit = int.TryParse(parameters[2], out var limitValue) ? limitValue : null;

                var leaderboardResponse = await gameApiClient.Leaderboards.GetLeaderboardScoresAsync(
                    context,
                    context.AccessToken,
                    new Guid(context.ProjectId),
                    leaderboardId,
                    false, offset, limit);
                if (leaderboardResponse.StatusCode != HttpStatusCode.OK)
                    throw new Exception(
                        $"Could not GetLeaderboardScoresAsync: Code: {leaderboardResponse.StatusCode} - {leaderboardResponse.ErrorText}");

                List<RankingConfig> rankingConfigs =
                    await RemoteConfigServices.GetPlayerRankingConfig(context, gameApiClient);
                var leaderboard = new HeadToHeadLeaderboard();
                var playerIds = leaderboardResponse.Data.Results.Select(entry => entry.PlayerId).ToArray();
                var playerInfos = await DatabaseService.GetPlayerInfoInBatch(context, playerIds);
                foreach (var entry in leaderboardResponse.Data.Results)
                {
                    var playerInfo = playerInfos[entry.PlayerId];
                    leaderboard.entries.Add(new HeadToHeadLeaderboardEntry
                    {
                        playerGuid = entry.PlayerId,
                        playerName = entry.PlayerName,
                        playerInfoModel = playerInfo,
                        rank = entry.Rank,
                        elo = (int)entry.Score,
                    });
                    if (entry.PlayerId == context.PlayerId)
                        leaderboard.playerRanking = entry.Rank;
                }

                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(leaderboard);
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Fail to get HeadToHead Leaderboard: {e}";
            }

            response.time = DateTime.Now;
            return response;
        }

        private async Task<APIServerResponse> ConsumeCoinFee(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse()
            {
                data = JsonConvert.SerializeObject(""),
                code = apiClientRequest.code
            };
            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var tourId = parameters[0];
                var configAssignmentHash = parameters[1];

                var tours = await DatabaseService.GetTourConfigs(GameMode.HeadToHead);

                var h2HTour = tours.Select(c => c.ConvertTo<H2HTourModel>()).ToList();

                var matchingTourConfig = h2HTour.FirstOrDefault(config => config.Uuid.ToString() == tourId);

                if (matchingTourConfig == null)
                    throw new Exception($"There is no matching h2h tour config. RankId: {tourId}");

                var getPlayerInfoTask = await DatabaseService.GetPlayerInfo(context);

                var playerRank = getPlayerInfoTask.rank.rankId;

                if (playerRank == null)
                {
                    throw new Exception($"Could not found RankId of user!");
                }

                var matchingFeeConfig = matchingTourConfig.EntryFees.FirstOrDefault(c => c.RankId == playerRank);

                if (matchingFeeConfig == null)
                {
                    var defaultConfig = matchingTourConfig.EntryFees.Find((ef) =>
                        ef.RankId == CloudCodeConstant.DefaultTourConfigRankId);
                    matchingFeeConfig = defaultConfig ?? throw new Exception($"Could not found matching fees config");
                }

                foreach (var item in matchingFeeConfig.Costs)
                {
                    var consumeCoinResponse = await gameApiClient.EconomyCurrencies.DecrementPlayerCurrencyBalanceAsync(
                        context,
                        context.AccessToken,
                        context.ProjectId,
                        context.PlayerId!,
                        item.ItemId,
                        new CurrencyModifyBalanceRequest(item.ItemId, item.Quantity),
                        configAssignmentHash
                    );
                    if (consumeCoinResponse.StatusCode != HttpStatusCode.OK)
                        throw new Exception($"Failed to consume ${item.ItemId}");
                }

                response.responseCode = APIResponseCode.Success;
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message = $"Failed H2H coin consume: {e}.";
            }

            response.time = DateTime.Now;
            return response;
        }

        private async Task<int> CalculateResultCoin(int baseAmount, GearBonusData gearBonusData)
        {
            List<GearInventoryItem> gearInventoryItems = await EconomyServices.GetEconomyConfigurationGearInventoryItems(
                context, 
                gameApiClient, 
                isServiceToken: false, 
                null, null
            );

            foreach (var gearId in gearBonusData.gearIds!)
            {
                var gearData = gearInventoryItems.FirstOrDefault(item => item.id == gearId);
                if (gearData != null)
                {
                    if (gearData.customData.type == GearType.CoinBoost)
                    {
                        CoinBoostGearData coinBoostGearData = (CoinBoostGearData)gearData.customData.GetGearData();
                        int strokeCount = gearBonusData.strokeCount;
                        int par = gearBonusData.par;

                        int scoreDiff = strokeCount - par;

                        if (strokeCount == 1)
                        {
                            return (int)(baseAmount * (coinBoostGearData.finalResultBonus4 + 1f));
                        }

                        switch (scoreDiff)
                        {
                            case -1:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus1 + 1f));
                            }
                            case -2:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus2 + 1f));
                            }
                            case -3:
                            {
                                return (int)(baseAmount * (coinBoostGearData.finalResultBonus3 + 1f));
                            }
                        }

                        return baseAmount;
                    }
                }
            }

            return baseAmount;
        }

        /// <summary>
        /// Get Leaderboard by player Ids
        /// </summary>
        /// <param name="apiClientRequest">apiClientRequest.data is json of data array</param>
        /// <param name="data[0]">leaderboardId</param>
        /// <param name="data[1]">Json of a List of Player id</param>
        /// <returns>Response to client, data contain Leaderboard as Json</returns>
        private async Task<APIServerResponse> GetLeaderboardByPlayerIds(APIClientRequest apiClientRequest)
        {
            var response = new APIServerResponse
            {
                data = JsonConvert.SerializeObject(string.Empty),
                code = apiClientRequest.code
            };

            var parameters = JsonConvert.DeserializeObject<List<string>>(apiClientRequest.data);
            if (parameters == null || parameters.Count == 0)
            {
                response.responseCode = APIResponseCode.Fail;
                response.time = DateTime.Now;
                response.message = "Data is null";
                return response;
            }

            try
            {
                var leaderboardId = parameters[0];
                var playerIdsJson = parameters[1];

                var playerIds = JsonConvert.DeserializeObject<List<string>>(playerIdsJson);
                if (playerIds == null)
                {
                    throw new Exception($"Could not parse Player Ids json: {playerIdsJson}");
                }

                var leaderboardResponse = await gameApiClient.Leaderboards.GetLeaderboardScoresByPlayerIdsAsync(
                    executionContext: context,
                    accessToken: context.AccessToken,
                    projectId: new Guid(context.ProjectId),
                    leaderboardId: leaderboardId,
                    includeMetadata: false,
                    leaderboardPlayerIds: new LeaderboardPlayerIds(playerIds));

                if (leaderboardResponse.StatusCode != HttpStatusCode.OK)
                    throw new Exception(
                        $"Could not GetLeaderboardScoresAsync: Code: {leaderboardResponse.StatusCode} - {leaderboardResponse.ErrorText}");

                if (leaderboardResponse.Data.Results == null)
                    throw new Exception("Entries not found");

                var playerInfos = await DatabaseService.GetPlayerInfoInBatch(context, playerIds.ToArray());
                var leaderboard = new HeadToHeadLeaderboard();
                foreach (var entry in leaderboardResponse.Data.Results)
                {
                    var playerInfo = playerInfos[entry.PlayerId];
                    leaderboard.entries.Add(new HeadToHeadLeaderboardEntry
                    {
                        playerGuid = entry.PlayerId,
                        playerName = entry.PlayerName,
                        playerInfoModel = playerInfo,
                        rank = entry.Rank,
                        elo = (int)entry.Score
                    });
                    if (entry.PlayerId == context.PlayerId)
                        leaderboard.playerRanking = entry.Rank;
                }

                response.responseCode = APIResponseCode.Success;
                response.data = JsonConvert.SerializeObject(leaderboard);
            }
            catch (Exception e)
            {
                response.responseCode = APIResponseCode.Fail;
                response.message += $"Fail to get HeadToHead Leaderboard: {e}";
            }

            response.time = DateTime.Now;
            return response;
        }
    }
}