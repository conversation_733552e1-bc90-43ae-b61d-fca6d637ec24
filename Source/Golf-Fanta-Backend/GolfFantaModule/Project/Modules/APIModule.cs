using System;
using System.Threading.Tasks;
using GolfFantaCore.Common;
using GolfFantaCore.DTO.Player;
using GolfFantaCore.Entities.Database;
using GolfFantaModule.CustomService.Interfaces;
using GolfFantaModule.Supabase.Services.Implements;
using GolfFantaModule.Supabase.Services.Interfaces;
using GolfFantaModule.UnityGameServices.Services.Interfaces;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Unity.Services.CloudCode.Apis;
using Unity.Services.CloudCode.Core;
using static Supabase.Postgrest.Constants;

namespace GolfFantaModule.Modules
{
    public abstract class APIModule
    {
        protected readonly ILogger<GolfFantaModule> logger;

        protected IExecutionContext context;
        protected IGameApiClient gameApiClient;
        protected IAdminApiClient adminApiClient;
        protected ICloudSaveServices CloudSaveServices;
        protected IRemoteConfigServices RemoteConfigServices;
        protected IEconomyServices EconomyServices;
        protected IInternalDatabaseService DatabaseService;
        protected ICustomServices CustomServices;
        protected APIClientRequest ApiClientRequest;
        
        public APIModule(
            ILogger<GolfFantaModule> _logger,
            IExecutionContext _context,
            IGameApiClient _gameApiClient,
            IAdminApiClient _adminApiClient,
            ICloudSaveServices cloudSaveServices,
            IRemoteConfigServices remoteConfigServices,
            IEconomyServices economyServices,
            IInternalDatabaseService internalDatabaseService,
            ICustomServices customServices,
            APIClientRequest apiClientRequest
        )
        {
            logger = _logger;

            context = _context;
            gameApiClient = _gameApiClient;
            adminApiClient = _adminApiClient;
            CloudSaveServices = cloudSaveServices;
            RemoteConfigServices = remoteConfigServices;
            EconomyServices = economyServices;
            DatabaseService = internalDatabaseService;
            CustomServices = customServices;
            ApiClientRequest = apiClientRequest;
        }

        public virtual async Task<string> Initialize(bool isCache)
        {
            await DatabaseService.Initialize(context, gameApiClient, isCache);
            return DatabaseService.GetUserId();
        }
        
        public abstract Task<APIServerResponse> Process();
        
        public async Task<bool> IsSessionValid(string sessionUuid)
        {
            if (string.IsNullOrEmpty(sessionUuid))
            {
                return false;
            }

            string dbUserId = DatabaseService.GetUserId();

            SessionEntity? latestSession = await DatabaseService.From<SessionEntity>()
                .Filter("user_id", Operator.Equals, dbUserId)
                .Order("created_at", Ordering.Descending)
                .Limit(1)
                .Single();

            return latestSession!.Uuid.Equals(sessionUuid);
        }

        public void TrackingActivity(PlayerActivityRequest request)
        {
            var customRequest = new APIClientRequest()
            {
                code = ApiClientRequest.code,
                requestId = ApiClientRequest.requestId,
                sessionId = ApiClientRequest.sessionId,
                time = ApiClientRequest.time,
                data = JsonConvert.SerializeObject(request)
            };
            CustomServices.Process(customRequest);
        }
        
        protected APIServerResponse Success(object? data = null, string message = "") => 
            new APIServerResponse(code: ApiClientRequest.code, responseCode: APIResponseCode.Success, time: DateTime.UtcNow, data: JsonUtils.ToJson(data), message: message);
        protected APIServerResponse Fail(string message, APIResponseCode responseCode = APIResponseCode.Fail) => 
            new APIServerResponse(code: ApiClientRequest.code, responseCode: responseCode, time: DateTime.UtcNow, data: string.Empty, message: message);
        protected APIServerResponse ConvertToClientResponse(CustomAPIServerResponse apiServerResponse) => 
            new APIServerResponse(code: ApiClientRequest.code, responseCode: apiServerResponse.responseCode, time: DateTime.UtcNow, data: apiServerResponse.data, message: apiServerResponse.message);
    }
}
