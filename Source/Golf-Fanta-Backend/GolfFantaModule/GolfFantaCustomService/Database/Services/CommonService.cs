using GolfFantaCore.Common.Enums;
using GolfFantaCore.DTOs;
using GolfFantaCore.Models.Common;
using GolfFantaCustomService.Utils;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services;

public class CommonService(DatabaseContext dbContext)
{
    public async Task InsertOrUpdatePlayerDevice(params PlayerDeviceModel[] playerDeviceModels)
    {
        foreach (var playerDevice in playerDeviceModels)
        {
            var existingEntity = await dbContext.PlayerDevices.FirstOrDefaultAsync(entity => entity.Uuid == playerDevice.Uuid);
            if (existingEntity != null)
            {
                existingEntity.UpdateFromDeviceModel(playerDevice);
            }
            else
            {
                var newEntity = playerDevice.ToPlayerDeviceEntity();
                dbContext.PlayerDevices.Add(newEntity);
            }
        }
        await dbContext.SaveChangesAsync();
    }

    public async Task<PlayerLightweightProfile?> GetPlayerLightweightProfile(string unityPlayerId)
    {
        var playerEntity = await dbContext.PlayersPublic
            .AsNoTracking()
            .Where(player => player.UnityPlayerGuid == unityPlayerId)
            .Select(player => new
            {
                player.UserId,
                player.UnityPlayerGuid,
                player.PlayerName,
                player.AvatarId,
                player.Elo
            })
            .FirstOrDefaultAsync();
        
        return playerEntity == null ? null : new PlayerLightweightProfile(playerEntity.UserId, playerEntity.UnityPlayerGuid, playerEntity.PlayerName, playerEntity.AvatarId, playerEntity.Elo);
    }

    public async Task<bool> HasSentFriendGift(Guid senderUuid, string receiverUnityPlayerId)
    {
        var receiverEntity = await dbContext.PlayersPublic
            .AsNoTracking()
            .Where(entity => entity.UnityPlayerGuid == receiverUnityPlayerId)
            .Select(entity => new { entity.UserId, entity.UnityPlayerGuid })
            .FirstOrDefaultAsync();

        if (receiverEntity == null)
            throw new KeyNotFoundException($"Player {receiverUnityPlayerId} does not exist");
        
        var utcStart = DateTime.UtcNow.Date;
        var utcEnd   = utcStart.AddDays(1); 
        
        var sentToday = await dbContext.PlayerMessages
            .Where(entity => 
                entity.SenderUuid == senderUuid 
                && entity.ReceiverUuid == receiverEntity.UserId
                && entity.CreatedAt >= utcStart
                && entity.CreatedAt < utcEnd
                && entity.MessageType == MessageType.Attached)
            .AnyAsync();

        return sentToday;
    }
}

