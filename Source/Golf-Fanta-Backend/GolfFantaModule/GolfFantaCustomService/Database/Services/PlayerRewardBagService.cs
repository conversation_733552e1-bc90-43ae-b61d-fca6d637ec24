using GolfFantaCustomService.Database.Entities.Bag;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services;

public class PlayerRewardBagService(DatabaseContext context)
{
    private DatabaseContext _context = context;

    public async Task<List<PlayerRewardBagEntity>> GetAllRewardEntities(CancellationToken cancellationToken)
    {
        return await _context.PlayerRewardBags.ToListAsync(cancellationToken);
    }
}