using GolfFantaCore.Common.Enums;
using GolfFantaCore.Models.Activity;
using GolfFantaCustomService.Database.Entities.PlayerActivity;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services;

public class PlayerActivityService(DatabaseContext context)
{
    private DatabaseContext _context = context;

    #region General

    public async Task InsertActivity(PlayerActivityEntity entity)
    {
        _context.PlayerActivities.Add(entity);
        await _context.SaveChangesAsync();
    }

    public async Task<List<PlayerActivityEntity>> InsertRangeActivities(List<PlayerActivityEntity> activityEntities,
        CancellationToken cancellationToken)
    {
        try
        {
            _context.PlayerActivities.AddRange(activityEntities);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception e)
        {
            return new List<PlayerActivityEntity>();
        }

        return activityEntities;
    }

    private async Task<List<PlayerActivityEntity>> GetOptimize(CancellationToken cancellationToken)
    {
        var data = await context.PlayerActivities
            .FromSqlInterpolated($@"
                SELECT *
                FROM   player_activity
                WHERE  processed = 'UnProcessed'
                ORDER  BY created_at
                LIMIT  100
                FOR UPDATE SKIP LOCKED")
            .ToListAsync(cancellationToken);
        return data;
    }
    
    public async Task<List<UserActivityInfo>> GetUserActivitiesByTypeAsync(
        List<Guid> userGuids,
        PlayerActivityType type,
        CancellationToken cancellationToken)
    {
        var data = await GetOptimize(cancellationToken);
        
        return data.Where(a => userGuids.Contains(a.UserId)
                               && a.ActivityType == type)
            .GroupBy(a => a.UserId)
            .Select(g => new UserActivityInfo
            {
                UserId = g.Key,
                HasUnprocessed = g.Any(x => x.ProcessStatus == ActivityProcessStatus.UnProcessed)
            }).ToList();
    }

    #endregion
}