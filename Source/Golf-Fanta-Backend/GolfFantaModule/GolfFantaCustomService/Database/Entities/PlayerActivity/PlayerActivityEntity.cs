using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using GolfFantaCore.Common.Enums;

namespace GolfFantaCustomService.Database.Entities.PlayerActivity;

[Table("player_activity")]
public class PlayerActivityEntity
{
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }
    
    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("activity_type")]
    public PlayerActivityType ActivityType { get; set; }
    
    [Column("processed")]
    public ActivityProcessStatus ProcessStatus { get; set; }
    
    [Column("data")]
    public string Data { get; set; }
}