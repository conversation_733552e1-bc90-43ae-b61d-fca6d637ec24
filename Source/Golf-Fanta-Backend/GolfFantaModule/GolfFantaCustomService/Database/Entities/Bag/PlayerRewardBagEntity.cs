using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GolfFantaCustomService.Database.Entities.Bag;

[Table("player_reward_bag")]
public class PlayerRewardBagEntity
{
    [Key]
    [Column("id")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    
    [Column("uuid")]
    public Guid BagUuid { get; set; }
    
    [Column("created_at")]
    public DateTime CreatedAt { get; set; }
    
    [Column("last_modified")]
    public DateTime LastModified { get; set; }

    [Column("user_id")]
    public Guid UserId { get; set; }
    
    [Column("bag_config_id")]
    public string BagConfigId { get; set; }
    
    [Column("skipped_by")]
    public string SkippedBy { get; set; }
    
    [Column("received_time")]
    public DateTime ReceivedTime { get; set; }
    
    [Column("opened_time")]
    public DateTime? OpenedTime { get; set; }
}