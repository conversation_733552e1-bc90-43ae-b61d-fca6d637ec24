using FirebaseAdmin;
using GolfFantaCore.Common;
using GolfFantaCore.Common.Utils;
using GolfFantaCustomService.Controllers;
using GolfFantaCustomService.Database;
using GolfFantaCustomService.Database.Services.Host;
using GolfFantaCustomService.Database.Services.Host.Jobs;
using Google.Apis.Auth.OAuth2;
using Microsoft.EntityFrameworkCore;
using Npgsql;

var builder = WebApplication.CreateBuilder(args);
NpgsqlConnection.GlobalTypeMapper.EnableDynamicJson();
// Get the connection string with placeholders
var rawConnectionString = builder.Configuration.GetConnectionString("DefaultConnection");
var rawFirebaseConnectionString = builder.Configuration.GetConnectionString("FirebaseConnection");

// Replace placeholders with environment variables
string resolvedConnectionString = ExpandEnvironmentVariables(rawConnectionString);
string resolvedFirebaseConnectionString = ExpandEnvironmentVariables(rawFirebaseConnectionString);

Console.WriteLine(resolvedConnectionString);

// Now use the resolved connection string
builder.Services.AddDbContext<DatabaseContext>(options =>
{
    options.UseNpgsql(resolvedConnectionString);
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen();
builder.Services.ConfigureHttpJsonOptions(options => {
    options.SerializerOptions.WriteIndented = true;
    options.SerializerOptions.IncludeFields = true;
});

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowLocalhost5173",
        policy =>
        {
            policy.WithOrigins("http://localhost:5173")
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
});

builder.Services.AddScoped<IBackgroundJob, ProcessPlayerActivityJob>();
builder.Services.AddScoped<IBackgroundJob, ProcessBagJob>();
builder.Services.AddHostedService<TimedHostedService>();

FirebaseApp.Create(new AppOptions
{
    Credential = GoogleCredential
        .FromJson(resolvedFirebaseConnectionString)
        .CreateScoped("https://www.googleapis.com/auth/firebase.messaging")
});

var app = builder.Build();

app.UseHttpsRedirection();

app.UseSwagger();

app.UseCors("AllowLocalhost5173");

app.UsePathBase("/custom-api");

app.UseSwaggerUI(c =>
{
    // c.RoutePrefix = "swagger";
    c.SwaggerEndpoint("v1/swagger.json", "API Docs");
});

app.MapPost("/custom", async (CustomAPIClientRequest request) =>
{
    var serviceProvider = app.Services;
    using var scope = serviceProvider.CreateScope();
    var dbContext = scope.ServiceProvider.GetRequiredService<DatabaseContext>();
    var response = new CustomAPIServerResponse();
    response.code = request.Code;

    try
    {
        var requestCode = (int)request.Code;

        BaseController controller = requestCode switch
        {
            >= 0 and < 100 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 100 and < 200 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 200 and < 300 => new CommonController(dbContext),
            >= 300 and < 400 => new MatchController(dbContext),
            >= 400 and < 500 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 500 and < 600 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 600 and < 700 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 700 and < 800 => throw new NotImplementedException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}"),
            >= 800 and < 1000 => new ChallengeController(dbContext),
            >= 1000 and < 1100 => new MessageController(dbContext),
            >= 1200 and < 1300 => new TourController(dbContext),
            _ => throw new ArgumentOutOfRangeException($"Unhandled request code: {EnumUtils.GetDisplayName(request.Code)}")
        };
        
        response = await controller.Handle(request);
    }
    catch (ArgumentOutOfRangeException e)
    {
        response.responseCode = APIResponseCode.Fail_CodeIneligible;
        response.message = e.Message;
    }
    catch (Exception e)
    {
        response.responseCode = APIResponseCode.Fail;
        response.message = e.Message;
    }
    
    response.time = DateTime.UtcNow;
    return response;
});

static string ExpandEnvironmentVariables(string input)
{
    if (string.IsNullOrEmpty(input)) return input;

    foreach (System.Collections.DictionaryEntry env in Environment.GetEnvironmentVariables())
    {
        string key = env.Key.ToString();
        string value = env.Value.ToString();
        input = input.Replace($"${{{key}}}", value);
    }
    return input;
}

app.Run();