using GolfFantaCore.Common.Enums;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services.Host.Jobs;

public class ProcessPlayerActivityJob : IBackgroundJob
{
    private readonly DatabaseContext _db;
    private readonly ILogger<ProcessPlayerActivityJob> _logger;
    private PlayerActivityService _activityService;

    public ProcessPlayerActivityJob(DatabaseContext db, ILogger<ProcessPlayerActivityJob> logger)
    {
        _db = db;
        _logger = logger;
        _activityService = new PlayerActivityService(db);
    }

    public async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        var activities = await _db.PlayerActivities
            .Where(a => a.ProcessStatus == ActivityProcessStatus.UnProcessed)
            .ToListAsync(cancellationToken);

        _logger.LogInformation("Unprocessed Activities: {Count}", activities.Count);
    }
}