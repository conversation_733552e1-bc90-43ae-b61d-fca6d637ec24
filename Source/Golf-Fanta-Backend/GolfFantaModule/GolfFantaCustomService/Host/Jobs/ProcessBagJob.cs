using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCustomService.Database.Entities.PlayerActivity;
using Microsoft.EntityFrameworkCore;

namespace GolfFantaCustomService.Database.Services.Host.Jobs;

public class ProcessBagJob : IBackgroundJob
{
    private readonly DatabaseContext _db;
    private readonly ILogger<ProcessBagJob> _logger;
    private PlayerActivityService _activityService;
    private PlayerRewardBagService _rewardBagService;
    public ProcessBagJob(DatabaseContext db, ILogger<ProcessBagJob> logger)
    {
        _db = db;
        _logger = logger;
        _activityService = new PlayerActivityService(db);
        _rewardBagService = new PlayerRewardBagService(db);
    }
    
    //TODO add check bag has countdown done
    public async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        var oneMonthAgo = DateTime.UtcNow.AddMonths(-1);
        var now = DateTime.UtcNow;

        // Step 1: Get users with 4 or more unopened bags in the last month
        var rewards = await _rewardBagService.GetAllRewardEntities(cancellationToken);
        var usersWith4UnopenedBags = rewards
            .Where(bag => bag.OpenedTime == null && bag.CreatedAt >= oneMonthAgo)
            .GroupBy(bag => bag.UserId)
            .Where(group => group.Count() >= 4)
            .Select(group => group.Key).ToList();

        if (usersWith4UnopenedBags.Count == 0)
        {
            return;
        }

        // Step 2: Fetch FullBag activities for those users
        var fullBagActivities = await _activityService.GetUserActivitiesByTypeAsync(usersWith4UnopenedBags,
            PlayerActivityType.FullBag, cancellationToken);
        
        // Step 3: Filter users eligible for new FullBag activity
        var newUserIds = usersWith4UnopenedBags
            .Where(userId =>
            {
                var activity = fullBagActivities.FirstOrDefault(a => a.UserId == userId);

                if (activity == null)
                    return true;

                if (activity.HasUnprocessed)
                    return false;

                return true;
            })
            .ToList();

        if (newUserIds.Count == 0)
        {
            return;
        }

        // Step 4: Create new activities
        var newActivities = newUserIds.Select(userId => new PlayerActivityEntity
        {
            CreatedAt = now,
            LastModified = now,
            UserId = userId,
            ActivityType = PlayerActivityType.FullBag,
            ProcessStatus = ActivityProcessStatus.UnProcessed,
            Data = JsonUtils.ToJson("Full Bag")
        }).ToList();
        
        var result = await _activityService.InsertRangeActivities(newActivities, cancellationToken);

        _logger.LogInformation("Added {Count} FullBag activity entries.", result.Count);
    }
}