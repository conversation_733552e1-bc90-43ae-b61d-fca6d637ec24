using GolfFantaCore.Common;
using GolfFantaCore.Common.Enums;
using GolfFantaCore.DTO.Player;
using GolfFantaCustomService.Database;
using GolfFantaCustomService.Database.Entities;
using GolfFantaCustomService.Database.Entities.PlayerActivity;
using GolfFantaCustomService.Database.Services;
using Newtonsoft.Json;

namespace GolfFantaCustomService.Controllers;

public class MatchController(DatabaseContext databaseContext) : BaseController
{
    private PlayerActivityService _activityService = new(databaseContext);

    public override async Task<CustomAPIServerResponse> Handle(CustomAPIClientRequest request)
    {
        switch (request.Code)
        {
            case APIRequestMethod.GetHeadToHeadMatchResult: return await GetHeadToHeadMatchResult(request);
            default: return Fail("Invalid player code.");
        }
    }

    private async Task<CustomAPIServerResponse> GetHeadToHeadMatchResult(CustomAPIClientRequest request)
    {
        var requestModel = JsonConvert.DeserializeObject<PlayerActivityRequest>(request.Data);

        if (requestModel == null)
        {
            return Success(null, "Request Tracking Fail!");
        }

        var entity = new PlayerActivityEntity()
        {
            ActivityType = requestModel.Type,
            Data = JsonUtils.ToJson(requestModel.Data),
            UserId = Guid.Parse(request.UserId),
            ProcessStatus = ActivityProcessStatus.UnProcessed,
            CreatedAt = DateTime.UtcNow
        };
        await _activityService.InsertActivity(entity);
        return Success(entity, "Track Success!");
    }
}