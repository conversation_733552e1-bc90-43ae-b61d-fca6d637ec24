using GolfFantaCore.Common;
using GolfFantaCore.Common.Extensions;
using GolfFantaCore.DTOs;
using GolfFantaCore.Models.Common;
using GolfFantaCustomService.Database;
using GolfFantaCustomService.Database.Services;
using Newtonsoft.Json;

namespace GolfFantaCustomService.Controllers;

public class CommonController(DatabaseContext databaseContext) : BaseController
{
    private ChallengeService _challengeService = new(databaseContext);
    private CommonService _commonService = new(databaseContext);
    
    public override async Task<CustomAPIServerResponse> Handle(CustomAPIClientRequest request)
    {
        switch (request.Code)
        {
            case APIRequestMethod.RegisterDevice: return await RegisterDevice(request);
            case APIRequestMethod.GetRelationships: return await GetRelationships(request);
            
            default: return Fail("Invalid func code.");
        }
    }

    private async Task<CustomAPIServerResponse> RegisterDevice(CustomAPIClientRequest request)
    {
        try
        {
            var requestData = JsonUtils.ParseFromJsonString<RegisterDeviceRequestData>(request.Data);
            if (requestData == null || string.IsNullOrEmpty(requestData.PlayerDeviceUuid))
                throw new Exception("Request Data is null");
            
            var userId = request.UserId;

            var playerDeviceGuid = Guid.Parse(requestData.PlayerDeviceUuid);
            var userGuid = Guid.Parse(userId);
            var playerDevice = new PlayerDeviceModel(playerDeviceGuid, userGuid, requestData.DeviceToken,
                requestData.DeviceInfo, requestData.PushEnabled);
            await _commonService.InsertOrUpdatePlayerDevice(playerDevice);
            
            return Success();
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }

    private async Task<CustomAPIServerResponse> GetRelationships(CustomAPIClientRequest request)
    {
        try
        {
            var relationshipDtos = JsonUtils.ParseFromJsonString<List<RelationshipDTO>>(request.Data);
            var errorMessage = string.Empty;
            var userId = request.UserId;
            var userGuid = Guid.Parse(userId);
            
            foreach (var relationshipDto in relationshipDtos)
            {
                var playerLightweightProfile = await _commonService.GetPlayerLightweightProfile(userId);
                if (playerLightweightProfile == null)
                {
                    errorMessage += $"Fail to get player profile id: {relationshipDto.PlayerId}";
                    continue;
                }

                relationshipDto.PlayerId = playerLightweightProfile.UnityPlayerId;
                relationshipDto.PlayerName = playerLightweightProfile.PlayerName;
                relationshipDto.AvatarId = playerLightweightProfile.AvatarId;
                relationshipDto.Elo = playerLightweightProfile.Elo;
                relationshipDto.GiftSent = await _commonService.HasSentFriendGift(userGuid, relationshipDto.PlayerId);
            }

            var responseData = new GetRelationshipsResponseData();
            responseData.RelationshipDTOs = relationshipDtos;
            return Success(responseData);
        }
        catch (Exception e)
        {
            return Fail(e.Message);
        }
    }
}